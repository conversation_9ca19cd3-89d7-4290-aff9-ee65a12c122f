# API Latest Incidents - T<PERSON><PERSON> liệu hướng dẫn

## Tổng quan
API này được tạo để lấy 3 vụ việc mới nhất từ các báo cáo có `jobType` với `quickReportTemplate.chartTypes` chứa "highlight". API hỗ trợ filter theo khu vực thông qua query parameter.

## Thông tin endpoint

- **URL**: `/api/v1.0/statistics/latest-incidents`
- **Method**: `POST`
- **Authentication**: Yêu cầu token trong header
- **Content-Type**: `application/json`

## Request Headers
```
token: your-auth-token
Content-Type: application/json
```

## Request Body

### Parameters
| Tham số | Kiểu dữ liệu | Bắt buộc | Mô tả |
|---------|--------------|----------|-------|
| `area` | ObjectId | Không | ID khu vực để filter vụ việc theo khu vực cụ thể |

### Ví dụ Request Body

#### 1. <PERSON><PERSON>y tất cả vụ việc (không filter)
```json
{}
```

#### 2. Filter theo khu vực
```json
{
  "area": "507f1f77bcf86cd799439011"
}
```

## Response Format

### Success Response (Code: 200)
```json
{
  "code": 200,
  "data": {
    "incidents": [
      {
        "_id": "report_id",
        "title": "Tiêu đề vụ việc",
        "description": "Mô tả chi tiết vụ việc",
        "reportType": "quick",
        "caseCode": "CASE001",
        "workStatus": "in_progress",
        "status": "submitted",
        "jobType": {
          "_id": "jobtype_id",
          "name": "Tên loại công việc",
          "description": "Mô tả loại công việc",
          "chartTypes": ["highlight"]
        },
        "createdBy": {
          "_id": "user_id",
          "name": "Tên người tạo báo cáo",
          "idNumber": "123456",
          "units": [
            {
              "_id": "unit_id",
              "name": "Tên đơn vị"
            }
          ],
          "areas": [
            {
              "_id": "area_id",
              "name": "Tên khu vực",
              "level": 1
            }
          ]
        },
        "unit": {
          "_id": "unit_id",
          "name": "Tên đơn vị"
        },
        "location": {
          "address": "Địa chỉ cụ thể",
          "coordinates": [105.123456, 21.654321],
          "areas": [
            {
              "_id": "area_id",
              "name": "Tên khu vực",
              "level": 2
            }
          ]
        },
        "metrics": {
          "customField1": "value1",
          "customField2": 123
        },
        "createdAt": 1640995200000,
        "updatedAt": 1640995200000,
        "timeAgo": "2 giờ trước"
      }
    ],
    "total": 1,
    "maxLimit": 3,
    "filter": {
      "area": "507f1f77bcf86cd799439011",
      "chartType": "highlight"
    },
    "generatedAt": 1640995200000
  }
}
```

### Error Response

#### 1. Tham số không hợp lệ (Code: 400)
```json
{
  "code": 400,
  "message": {
    "head": "Thông báo",
    "body": "Bạn vui lòng kiểm tra lại dữ liệu vừa nhập. Xin cảm ơn."
  }
}
```

#### 2. Lỗi hệ thống (Code: 500)
```json
{
  "code": 500,
  "message": {
    "head": "Thông báo",
    "body": "Hệ thống đang bận vui lòng thử lại"
  }
}
```

#### 3. Token hết hạn (Code: 1993)
```json
{
  "code": 1993,
  "message": {
    "head": "Thông báo",
    "body": "Token đã hết hạn, vui lòng đăng nhập lại"
  }
}
```

## Response Fields Giải thích

### Incidents Array
- `_id`: ID của báo cáo
- `title`: Tiêu đề vụ việc
- `description`: Mô tả chi tiết
- `reportType`: Loại báo cáo ("quick" hoặc "detail")
- `caseCode`: Mã vụ việc (có thể null)
- `workStatus`: Trạng thái công việc
- `status`: Trạng thái báo cáo
- `jobType`: Thông tin loại công việc
- `createdBy`: Thông tin người tạo báo cáo
- `unit`: Thông tin đơn vị
- `location`: Thông tin địa điểm (có thể null)
- `metrics`: Dữ liệu số liệu tùy chỉnh
- `createdAt`: Timestamp tạo báo cáo
- `updatedAt`: Timestamp cập nhật cuối
- `timeAgo`: Chuỗi mô tả thời gian dễ đọc

### Summary Data
- `total`: Số lượng vụ việc trả về
- `maxLimit`: Giới hạn tối đa (luôn là 3)
- `filter`: Thông tin filter đã áp dụng
- `generatedAt`: Timestamp tạo response

## Cách sử dụng

### 1. Lấy vụ việc mới nhất cho dashboard
```javascript
const response = await fetch('/api/v1.0/statistics/latest-incidents', {
  method: 'POST',
  headers: {
    'token': 'your-auth-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({})
});

const data = await response.json();
console.log('Số vụ việc:', data.data.total);
console.log('Danh sách vụ việc:', data.data.incidents);
```

### 2. Lấy vụ việc theo khu vực cụ thể
```javascript
const response = await fetch('/api/v1.0/statistics/latest-incidents', {
  method: 'POST',
  headers: {
    'token': 'your-auth-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    area: '507f1f77bcf86cd799439011'
  })
});

const data = await response.json();
console.log('Vụ việc trong khu vực:', data.data.incidents);
```

## Lưu ý quan trọng

1. **Giới hạn kết quả**: API luôn trả về tối đa 3 vụ việc mới nhất
2. **Filter theo chartType**: Chỉ lấy các vụ việc có `jobType` với `chartTypes` chứa "highlight"
3. **Sắp xếp**: Kết quả được sắp xếp theo `createdAt` giảm dần (mới nhất trước)
4. **Trạng thái báo cáo**: Không lấy các báo cáo có status là "draft"
5. **Filter khu vực**: Khi có tham số `area`, sẽ filter theo cả `details.location.areas` và `createdBy.areas`

## Test API

Sử dụng file `test_latest_incidents_api.js` để test API:

```bash
node test_latest_incidents_api.js
```

Nhớ cập nhật `TEST_TOKEN` và `API_BASE_URL` trong file test trước khi chạy.
