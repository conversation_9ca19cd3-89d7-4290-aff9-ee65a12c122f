const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const Report = require('../../../models/report');
const JobType = require('../../../models/jobType');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy 3 vụ việc mới nhất (Latest Incidents)
 * POST /api/v1.0/statistics/latest-incidents
 *
 * Trả về 3 vụ việc mới nhất từ các báo cáo có jobType với chartTypes chứa "highlight"
 * Có thể filter theo khu vực thông qua query parameter
 * Dữ liệu này được sử dụng để hiển thị các vụ việc nổi bật mới nhất trên dashboard
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const {
    area // ID khu vực để filter (optional)
  } = req.body;

  let highlightJobTypes = [];
  let latestIncidents = [];

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      area: Joi.objectId().optional() // Khu vực filter là optional
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: `Tham số không hợp lệ: ${error.details[0].message}`
        }
      });
    }

    next();
  };

  /**
   * Lấy danh sách JobType có chartTypes chứa "highlight"
   */
  const getHighlightJobTypes = (next) => {
    JobType.find({
      'quickReportTemplate.chartTypes': 'highlight',
      status: 1,
      deletedAt: { $exists: false }
    })
    .select('_id name quickReportTemplate')
    .lean()
    .exec((err, jobTypes) => {
      if (err) {
        return next(err);
      }

      if (!jobTypes || jobTypes.length === 0) {
        // Nếu không có JobType nào có chartTypes "highlight", trả về response hoàn chỉnh
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            incidents: [],
            total: 0,
            maxLimit: 3,
            filter: {
              area: area || null,
              chartType: 'highlight'
            },
            generatedAt: Date.now(),
            message: 'Không có loại công việc nào được đánh dấu là highlight'
          }
        });
      }

      highlightJobTypes = jobTypes;
      next();
    });
  };

  /**
   * Lấy 3 vụ việc mới nhất từ các báo cáo có jobType highlight
   */
  const getLatestIncidents = (next) => {
    if (highlightJobTypes.length === 0) {
      return next(); // Skip nếu không có highlight job types
    }

    const mongoose = require('mongoose');
    const highlightJobTypeIds = highlightJobTypes.map(jt => jt._id);

    // Tạo query cơ bản
    const query = {
      jobType: { $in: highlightJobTypeIds },
      deletedAt: { $exists: false },
      status: { $ne: 'draft' } // Không lấy báo cáo nháp
    };

    // Thêm filter theo khu vực nếu có
    if (area) {
      try {
        const areaObjectId = mongoose.Types.ObjectId(area);
        query.$or = [
          // Filter theo khu vực trong details.location.areas
          { 'details.location.areas': areaObjectId },
          // Hoặc filter theo khu vực của người tạo báo cáo
          { 'createdBy.areas': areaObjectId }
        ];
      } catch (err) {
        // Nếu area ID không hợp lệ, bỏ qua filter
        console.warn('Invalid area ObjectId:', area);
      }
    }

    Report.find(query)
      .populate({
        path: 'jobType',
        select: 'name description quickReportTemplate'
      })
      .populate({
        path: 'createdBy',
        select: 'name idNumber units areas',
        populate: [
          {
            path: 'units',
            select: 'name'
          },
          {
            path: 'areas',
            select: 'name level'
          }
        ]
      })
      .populate({
        path: 'details.location.areas',
        select: 'name level'
      })
      .populate({
        path: 'unit',
        select: 'name'
      })
      .sort({ createdAt: -1 }) // Sắp xếp theo thời gian tạo mới nhất
      .limit(3) // Chỉ lấy 3 vụ việc mới nhất
      .lean()
      .exec((err, reports) => {
        if (err) {
          return next(err);
        }

        latestIncidents = reports || [];
        next();
      });
  };

  /**
   * Format dữ liệu response
   */
  const formatResponse = (next) => {
    // Format dữ liệu cho từng vụ việc
    const formattedIncidents = latestIncidents.map(incident => ({
      _id: incident._id,
      title: incident.title || 'Không có tiêu đề',
      description: incident.description || '',
      reportType: incident.reportType,
      caseCode: incident.caseCode,
      workStatus: incident.workStatus,
      status: incident.status,

      // Thông tin loại công việc
      jobType: {
        _id: incident.jobType._id,
        name: incident.jobType.name,
        description: incident.jobType.description,
        chartTypes: incident.jobType.quickReportTemplate?.chartTypes || []
      },

      // Thông tin người tạo
      createdBy: {
        _id: incident.createdBy._id,
        name: incident.createdBy.name,
        idNumber: incident.createdBy.idNumber,
        units: incident.createdBy.units || [],
        areas: incident.createdBy.areas || []
      },

      // Thông tin đơn vị
      unit: incident.unit ? {
        _id: incident.unit._id,
        name: incident.unit.name
      } : null,

      // Thông tin địa điểm (nếu có)
      location: incident.details && incident.details.length > 0 ? {
        address: incident.details[0].location?.address || '',
        coordinates: incident.details[0].location?.coordinates || null,
        areas: incident.details[0].location?.areas || []
      } : null,

      // Metrics (dữ liệu số liệu)
      metrics: incident.metrics || {},

      // Thời gian
      createdAt: incident.createdAt,
      updatedAt: incident.updatedAt,

      // Thời gian hiển thị dễ đọc
      timeAgo: getTimeAgo(incident.createdAt)
    }));

    // Tạo response data
    const responseData = {
      incidents: formattedIncidents,
      total: formattedIncidents.length,
      maxLimit: 3,
      filter: {
        area: area || null,
        chartType: 'highlight'
      },
      generatedAt: Date.now()
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: responseData
    });
  };

  /**
   * Ghi log hoạt động
   */
  const writeLog = (result, next) => {
    // Ghi log hoạt động nếu có SystemLogModel
    if (global.SystemLogModel) {
      global.SystemLogModel.create({
        user: userId,
        action: 'get_latest_incidents',
        description: 'Xem 3 vụ việc mới nhất',
        data: req.body,
        updatedData: {
          totalIncidents: result.data.total,
          hasAreaFilter: !!area,
          areaId: area,
          highlightJobTypesCount: highlightJobTypes.length
        }
      }, () => {});
    }

    next(null, result);
  };

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    getHighlightJobTypes,
    getLatestIncidents,
    formatResponse,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};

/**
 * Helper function: Tính toán thời gian "time ago" dễ đọc
 * @param {Number} timestamp - Timestamp tạo báo cáo
 * @returns {String} Chuỗi mô tả thời gian
 */
function getTimeAgo(timestamp) {
  const now = Date.now();
  const diff = now - timestamp;

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) {
    return 'Vừa xong';
  } else if (minutes < 60) {
    return `${minutes} phút trước`;
  } else if (hours < 24) {
    return `${hours} giờ trước`;
  } else if (days < 7) {
    return `${days} ngày trước`;
  } else {
    return new Date(timestamp).toLocaleDateString('vi-VN');
  }
}
