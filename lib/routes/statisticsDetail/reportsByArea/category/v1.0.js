const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');
const AreaModel = require('../../../../models/area');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê theo lĩnh vực trong khu vực
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê theo các lĩnh vực cụ thể trong khu vực
 * Các lĩnh vực: Cháy, Mâu thuẫn, Vi phạm, TNGT
 * Sử dụng trường summary và metrics trong ReportModel
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê theo lĩnh vực
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const areaId = req.body.area;

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;
  let areaInfo;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const options = ['3days','7days','week','month','year'];
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    if(!areaId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area là bắt buộc'
        }
      });
    }
    if(!mongoose.Types.ObjectId.isValid(areaId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số area không hợp lệ'
        }
      });
    }
    next();
  };

  const checkAreaExists = (next) => {
    // Kiểm tra xem area có tồn tại không
    AreaModel.findById(areaId)
      .then((area) => {
        if (!area) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,    
            message: {
              head: 'Lỗi tham số',
              body: 'Khu vực không tồn tại'
            }
          });
        }
        // Lưu thông tin area để sử dụng sau
        areaInfo = area;
        next();
      })
      .catch((err) => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: {
            head: 'Lỗi hệ thống',
            body: 'Đã xảy ra lỗi khi kiểm tra khu vực'
          }
        });
      });
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    next();
  };

  const getStatisticDocument = (next) => {
    // Query ReportModel để lấy thống kê theo lĩnh vực dựa trên summary.area và metrics
    const reportQuery = {
      createdAt: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      },
      summary: {
        $elemMatch: {
          area: mongoose.Types.ObjectId(areaId),
          count: { $gt: 0 }
        }
      }
    };

    ReportModel.aggregate([
      { $match: reportQuery },
      {
        $unwind: '$summary'
      },
      {
        $match: {
          'summary.area': mongoose.Types.ObjectId(areaId),
          'summary.count': { $gt: 0 }
        }
      },
      {
        $group: {
          _id: null,
          // Tính tổng số lượng theo từng lĩnh vực dựa trên loại metrics có trong report
          totalFireCount: { 
            $sum: { 
              $cond: [
                { $gt: [{ $ifNull: ['$metrics.fireCount', 0] }, 0] },
                '$summary.count',
                0
              ]
            }
          },
          totalConflictCount: { 
            $sum: { 
              $cond: [
                { $gt: [{ $ifNull: ['$metrics.conflictCount', 0] }, 0] },
                '$summary.count',
                0
              ]
            }
          },
          totalViolateCount: { 
            $sum: { 
              $cond: [
                { $gt: [{ $ifNull: ['$metrics.violateCount', 0] }, 0] },
                '$summary.count',
                0
              ]
            }
          },
          totalAccidentCount: { 
            $sum: { 
              $cond: [
                { $gt: [{ $ifNull: ['$metrics.accidentCount', 0] }, 0] },
                '$summary.count',
                0
              ]
            }
          },
          totalSummaryCount: { $sum: '$summary.count' }
        }
      }
    ])
      .then((aggregateResults) => {
        const stats = aggregateResults.length > 0 ? aggregateResults[0] : {
          totalFireCount: 0,
          totalConflictCount: 0,
          totalViolateCount: 0,
          totalAccidentCount: 0,
          totalSummaryCount: 0
        };

        // Tạo metrics theo các lĩnh vực
        const metrics = [
          {
            category: 'fire',
            categoryName: 'Số vụ cháy',
            count: stats.totalFireCount,
            color: '#FF4444'
          },
          {
            category: 'conflict',
            categoryName: 'Số vụ mâu thuẫn',
            count: stats.totalConflictCount,
            color: '#FF8800'
          },
          {
            category: 'violate',
            categoryName: 'Số vụ vi phạm',
            count: stats.totalViolateCount,
            color: '#FFAA00'
          },
          {
            category: 'accident',
            categoryName: 'Số vụ TNGT',
            count: stats.totalAccidentCount,
            color: '#AA0000'
          }
        ];

        // Tính tổng tất cả các lĩnh vực
        const totalByCategory = stats.totalFireCount + stats.totalConflictCount + 
                               stats.totalViolateCount + stats.totalAccidentCount;

        result = {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            title: `Thống kê theo lĩnh vực tại ${areaInfo.name}`,
            summary: {
              totalReports: stats.totalSummaryCount,
              totalByCategory: totalByCategory,
              areaName: areaInfo.name
            },
            chartConfig: {
              colors: {
                fire: '#FF4444',
                conflict: '#FF8800',
                violate: '#FFAA00',
                accident: '#AA0000'
              },
              labels: {
                fire: 'Số vụ cháy',
                conflict: 'Số vụ mâu thuẫn',
                violate: 'Số vụ vi phạm',
                accident: 'Số vụ TNGT'
              }
            },
            metrics,
            timeRange: {
              startTime: startTime.getTime(),
              endTime: new Date(endTime).getTime(),
              type
            }
          }
        };

        next(null, result);
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    checkAreaExists,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
