const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const JobTypeModel = require('../../../../models/jobType');
const ReportModel = require('../../../../models/report');

const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');

/**
 * API thống kê tổng quan an ninh
 * POST /api/v1.0/statistics/reports-summary
 *
 * Trả về thống kê tổng quan về các vụ việc an ninh trong hệ thống
 * Bao gồm:
 * - metrics.accidentCount: Số vụ tai nạn
 * - metrics.conflictCount: Số vụ mâu thuẫn  
 * - metrics.violateCount: Số vụ vi phạm
 * - metrics.fireCount: Số vụ cháy nổ
 * - Tổng số vụ việc = tổng tất cả các chỉ số trên
 * Dữ liệu này được sử dụng để hiển thị dashboard thống kê an ninh
 */
module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    '3days','7days','week','month','year',
  ]

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    
    next();
  };

  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
          }
        });
    }

    next();
  };

  const getStatisticDocument = (next) => {
    // B1: Query JobTypeModel để lấy các jobType có chartTypes = 'heatmap'
    JobTypeModel.find({ status : 1, 'quickReportTemplate.chartTypes' : 'heatmap' }, '_id name quickReportTemplate')
      .lean()
      .then((jobTypes) => {
        if (!jobTypes || jobTypes.length === 0) {
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: MESSAGES.SYSTEM.SUCCESS,
            data: {
              metrics: []
            }
          });
        }

        const jobTypeIds = jobTypes.map(jt => jt._id);
        
        // B2: Query ReportModel để lấy thống kê
        const reportQuery = {
          jobType: { $in: jobTypeIds },
          createdAt: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          }
        };
        
        // Tạo pipeline aggregate dựa vào type để phân chia thời gian
        let dateGrouping;
        let sortField;
        
        switch (type) {
          case '3days':
          case '7days':
            // Chia theo từng ngày
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } },
              day: { $dayOfMonth: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1, '_id.jobType': 1 };
            break;
          case 'week':
            // Chia theo từng thứ trong tuần (1=Chủ nhật, 2=Thứ 2, ...)
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              week: { $week: { $toDate: '$createdAt' } },
              dayOfWeek: { $dayOfWeek: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.week': 1, '_id.dayOfWeek': 1, '_id.jobType': 1 };
            break;
          case 'month':
            // Chia theo từng tuần trong tháng
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } },
              week: { $week: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.week': 1, '_id.jobType': 1 };
            break;
          case 'year':
            // Chia theo từng tháng trong năm
            dateGrouping = {
              year: { $year: { $toDate: '$createdAt' } },
              month: { $month: { $toDate: '$createdAt' } }
            };
            sortField = { '_id.year': 1, '_id.month': 1, '_id.jobType': 1 };
            break;
        }

        ReportModel.aggregate([
          { $match: reportQuery },
          {
            $group: {
              _id: {
                jobType: '$jobType',
                ...dateGrouping
              },
              accidentCount: {
                $sum: { $ifNull: ['$metrics.accidentCount', 0] }
              },
              conflictCount: {
                $sum: { $ifNull: ['$metrics.conflictCount', 0] }
              },
              violateCount: {
                $sum: { $ifNull: ['$metrics.violateCount', 0] }
              },
              fireCount: {
                $sum: { $ifNull: ['$metrics.fireCount', 0] }
              }
            }
          },
          { $sort: sortField }
        ])
          .then((reportStats) => {
            // Helper function để tạo tất cả time periods cho type
            const generateAllTimePeriods = () => {
              const periods = [];
              const start = new Date(startTime);
              const end = new Date(endTime);
              
              switch (type) {
                case '3days':
                case '7days':
                  // Tạo tất cả các ngày trong khoảng thời gian
                  for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                    periods.push({
                      timeLabel: `${d.getDate()}/${d.getMonth() + 1}/${d.getFullYear()}`,
                      period: {
                        year: d.getFullYear(),
                        month: d.getMonth() + 1,
                        day: d.getDate()
                      }
                    });
                  }
                  break;
                case 'week':
                  // Tạo tất cả các thứ trong tuần (T2 -> CN)
                  const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
                  for (let i = 0; i < 7; i++) {
                    periods.push({
                      timeLabel: dayNames[i],
                      period: {
                        dayOfWeek: i === 6 ? 0 : i + 1 // T2=1, T3=2, ..., T7=6, CN=0
                      }
                    });
                  }
                  break;
                case 'month':
                  // Tạo tất cả các tuần trong tháng
                  const startWeek = Math.floor((start.getTime() - new Date(start.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  const endWeek = Math.floor((end.getTime() - new Date(end.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
                  for (let w = startWeek; w <= endWeek; w++) {
                    periods.push({
                      timeLabel: `Tuần ${w - startWeek + 1}`,
                      period: {
                        week: w
                      }
                    });
                  }
                  break;
                case 'year':
                  // Tạo tất cả các tháng trong năm
                  for (let m = start.getMonth() + 1; m <= end.getMonth() + 1; m++) {
                    periods.push({
                      timeLabel: `Tháng ${m}`,
                      period: {
                        month: m
                      }
                    });
                  }
                  break;
              }
              return periods;
            };

            // Helper function để format label thời gian
            const formatTimeLabel = (timeGroup) => {
              switch (type) {
                case '3days':
                case '7days':
                  return `${timeGroup.day}/${timeGroup.month}/${timeGroup.year}`;
                case 'week':
                  const dayNames = ['CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
                  return dayNames[timeGroup.dayOfWeek];
                case 'month':
                  return `Tuần ${timeGroup.week - Math.floor((new Date(timeGroup.year, timeGroup.month - 1, 1).getTime() - new Date(timeGroup.year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}`;
                case 'year':
                  return `Tháng ${timeGroup.month}`;
                default:
                  return '';
              }
            };

            // Helper function để tính startTime và endTime cho từng time period
            const calculatePeriodTimeRange = (period) => {
              let periodStart, periodEnd;
              
              switch (type) {
                case '3days':
                case '7days':
                  // Cho từng ngày
                  periodStart = new Date(period.year, period.month - 1, period.day, 0, 0, 0);
                  periodEnd = new Date(period.year, period.month - 1, period.day, 23, 59, 59);
                  break;
                case 'week':
                  // Cho từng thứ trong tuần (T2 -> CN)
                  const weekStart = new Date(startTime);
                  periodStart = new Date(weekStart);
                  // Tính offset: T2=0, T3=1, ..., T7=5, CN=6
                  const offset = period.dayOfWeek === 0 ? 6 : period.dayOfWeek - 1;
                  periodStart.setDate(weekStart.getDate() + offset);
                  periodStart.setHours(0, 0, 0, 0);
                  periodEnd = new Date(periodStart);
                  periodEnd.setHours(23, 59, 59, 999);
                  break;
                case 'month':
                  // Cho từng tuần trong tháng
                  const yearStart = new Date(startTime.getFullYear(), 0, 1);
                  periodStart = new Date(yearStart.getTime() + (period.week - 1) * 7 * 24 * 60 * 60 * 1000);
                  periodEnd = new Date(periodStart.getTime() + 6 * 24 * 60 * 60 * 1000);
                  periodEnd.setHours(23, 59, 59, 999);
                  break;
                case 'year':
                  // Cho từng tháng trong năm
                  periodStart = new Date(startTime.getFullYear(), period.month - 1, 1, 0, 0, 0);
                  periodEnd = new Date(startTime.getFullYear(), period.month, 0, 23, 59, 59);
                  break;
                default:
                  periodStart = new Date(startTime);
                  periodEnd = new Date(endTime);
              }
              
              return {
                startTime: periodStart.getTime(),
                endTime: periodEnd.getTime()
              };
            };

            // Tạo tất cả time periods
            const allTimePeriods = generateAllTimePeriods();

            // Khởi tạo metrics với tất cả time periods (với giá trị 0)
            const metrics = allTimePeriods.map(period => {
              const timeRange = calculatePeriodTimeRange(period.period);
              return {
                timeLabel: period.timeLabel,
                period: period.period,
                startTime: timeRange.startTime,
                endTime: timeRange.endTime,
                accidentCount: 0,
                conflictCount: 0,
                violateCount: 0,
                fireCount: 0,
                totalCount: 0
              };
            });
            
            // Cập nhật dữ liệu thực tế từ reportStats
            reportStats.forEach(stat => {
              const timeLabel = formatTimeLabel(stat._id);
              
              const timeSeriesItem = metrics.find(ts => ts.timeLabel === timeLabel);
              if (timeSeriesItem) {
                timeSeriesItem.accidentCount += stat.accidentCount;
                timeSeriesItem.conflictCount += stat.conflictCount;
                timeSeriesItem.violateCount += stat.violateCount;
                timeSeriesItem.fireCount += stat.fireCount;
                timeSeriesItem.totalCount = timeSeriesItem.accidentCount + timeSeriesItem.conflictCount + timeSeriesItem.violateCount + timeSeriesItem.fireCount;
              }
            });

            // Tính tổng summary
            const summary = {
              accidentCount: metrics.reduce((sum, item) => sum + item.accidentCount, 0),
              conflictCount: metrics.reduce((sum, item) => sum + item.conflictCount, 0),
              violateCount: metrics.reduce((sum, item) => sum + item.violateCount, 0),
              fireCount: metrics.reduce((sum, item) => sum + item.fireCount, 0),
              incidentCount: metrics.reduce((sum, item) => sum + item.totalCount, 0)
            };

            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: "Biểu đồ thống kê an ninh",
                chartConfig: {
                  colors: {
                    totalCount: '#3498DB'        // xanh lam - tổng số vụ việc
                  },
                  labels: {
                    totalCount: 'Tổng số vụ việc'
                  }
                },
                summary,
                metrics,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              },
              "dataSummary": {
                title: "Thống kê theo lĩnh vực",
                chartConfig: {
                  colors: {
                    accidentCount: '#FF6B35',    
                    conflictCount: '#E74C3C',    
                    violateCount: '#F39C12',     
                    fireCount: '#C0392B',       
                  },
                  labels: {
                    accidentCount: 'Số vụ tai nạn',
                    conflictCount: 'Số vụ mâu thuẫn',
                    violateCount: 'Số vụ vi phạm',
                    fireCount: 'Số vụ cháy nổ',
                  },
                },
                summary,
                metrics,
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    calculateTimeRange,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
